import { IconCheckboxTick, IconImage } from "@douyinfe/semi-icons";
import React from "react";
import { formatDate } from "utils";
import { FinishItem, FinishListDisplayProps } from "./types";

/**
 * FinishListDisplay component for displaying completed approval records
 * Renders finishList data in format: "{approveTime}: {approveUser.name}完成[icon]"
 * Supports click events when imageList is not empty
 */
export const FinishListDisplay: React.FC<FinishListDisplayProps> = ({
  finishList,
  onImageClick,
}) => {
  // Handle empty or invalid finishList
  if (!finishList || finishList.length === 0) {
    return null;
  }

  /**
   * Handles click event for finish item
   * Only triggers callback if imageList is not empty
   */
  const handleItemClick = (item: FinishItem) => {
    if (item.imageList && item.imageList.length > 0) {
      onImageClick(item.imageList);
    }
  };

  /**
   * Determines if item is clickable based on imageList
   */
  const isClickable = (item: FinishItem): boolean => {
    return item.imageList && item.imageList.length > 0;
  };

  return (
    <div className="mt-2">
      {finishList.map((item: FinishItem, index: number) => {
        const clickable = isClickable(item);

        return (
          <div
            key={index}
            className={
              clickable
                ? "flex items-center my-1 p-2 bg-gray-50 rounded cursor-pointer transition-colors hover:bg-gray-100"
                : "flex items-center my-1 p-2 bg-gray-50 rounded opacity-60"
            }
            onClick={() => handleItemClick(item)}
            role={clickable ? "button" : undefined}
            tabIndex={clickable ? 0 : undefined}
            onKeyDown={
              clickable
                ? (e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      handleItemClick(item);
                    }
                  }
                : undefined
            }
          >
            <span className="flex-1 text-sm text-gray-600">
              {formatDate(item.approveTime as any) || item.approveTime}:{" "}
              {item.approveUser?.name || "未知用户"}完成
            </span>

            <IconCheckboxTick className="ml-2 text-green-600" size="small" />

            {clickable && (
              <IconImage
                className="ml-1 text-blue-600 hover:text-blue-800 cursor-pointer"
                title="查看相关图片"
                size="small"
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default FinishListDisplay;
