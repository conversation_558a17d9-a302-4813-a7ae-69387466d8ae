import { getJobSliceProgress } from "api";
import { jobSliceInfoAtom } from "atoms/specialWork";
import { ProgressTimeline } from "components";
import { useProgressData } from "hooks";

// 作业进展
export const ProgressRecordTab = () => {
  const { data, isLoading } = useProgressData({
    apiFunction: (id: string | number) => getJobSliceProgress(Number(id)),
    atom: jobSliceInfoAtom,
    queryKey: "getJobSliceProgress",
  });

  return <ProgressTimeline data={data} isLoading={isLoading} />;
};
