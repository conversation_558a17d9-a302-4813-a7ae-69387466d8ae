import { getAppointmentProgress } from "api";
import { jobAppointmentInfoAtom } from "atoms/specialWork";
import { ProgressTimeline } from "components";
import { useProgressData } from "hooks";

// 预约进展
export const AppointmentProgressRecordTab = () => {
  const { data, isLoading } = useProgressData({
    apiFunction: getAppointmentProgress,
    atom: jobAppointmentInfoAtom,
    queryKey: "getAppointmentProgress",
  });

  return <ProgressTimeline data={data} isLoading={isLoading} />;
};
